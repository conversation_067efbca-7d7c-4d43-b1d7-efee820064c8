MOOC序列相似度详细分析报告
==================================================

1. 数据集基本统计
用户总数: 35760
唯一项目数: 1238
序列模式统计: {'repeated_items': 0, 'unique_sequences': 29092, 'avg_unique_items_per_seq': 8.***************, 'sequence_diversity': 0.8135346756152125}

2. 项目频率统计
Top 10 热门项目:
  1. 项目18: 6612次
  2. 项目733: 5674次
  3. 项目944: 5659次
  4. 项目942: 5635次
  5. 项目943: 5634次
  6. 项目873: 5627次
  7. 项目15: 5592次
  8. 项目11: 4997次
  9. 项目19: 4356次
  10. 项目794: 3679次

3. 相似度详细统计
余弦相似度: 均值=0.0376, 标准差=0.0648
Jaccard相似度: 均值=0.0283, 标准差=0.0474
重叠比例: 均值=0.0361, 标准差=0.0591

4. 聚类分析结果
聚类0: 34个用户
聚类1: 84个用户
聚类2: 26个用户
聚类3: 26个用户
聚类4: 30个用户
