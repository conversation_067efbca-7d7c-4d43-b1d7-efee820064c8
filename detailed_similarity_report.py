#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的序列相似度分析报告
生成更全面的分析结果和可视化
"""

import numpy as np
import pandas as pd
from collections import Counter, defaultdict
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

def load_sequences(file_path):
    """加载序列数据"""
    sequences = {}
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split()
            if len(parts) >= 2:
                user_id = int(parts[0])
                sequence = [int(x) for x in parts[1:]]
                sequences[user_id] = sequence
    return sequences

def analyze_item_frequency(sequences):
    """分析项目频率分布"""
    item_counts = Counter()
    for seq in sequences.values():
        item_counts.update(seq)
    
    return item_counts

def analyze_sequence_patterns(sequences):
    """分析序列模式"""
    patterns = {
        'repeated_items': 0,
        'unique_sequences': 0,
        'avg_unique_items_per_seq': 0,
        'sequence_diversity': 0
    }
    
    unique_sequences = set()
    total_unique_items = 0
    
    for seq in sequences.values():
        # 检查重复项目
        if len(seq) != len(set(seq)):
            patterns['repeated_items'] += 1
        
        # 序列唯一性
        seq_tuple = tuple(seq)
        unique_sequences.add(seq_tuple)
        
        # 每个序列的唯一项目数
        total_unique_items += len(set(seq))
    
    patterns['unique_sequences'] = len(unique_sequences)
    patterns['avg_unique_items_per_seq'] = total_unique_items / len(sequences)
    patterns['sequence_diversity'] = len(unique_sequences) / len(sequences)
    
    return patterns

def calculate_similarity_metrics(sequences, sample_size=200):
    """计算多种相似度指标"""
    # 采样以提高计算效率
    sample_sequences = dict(list(sequences.items())[:sample_size])
    user_ids = list(sample_sequences.keys())
    n_users = len(user_ids)
    
    # 初始化相似度矩阵
    cosine_matrix = np.zeros((n_users, n_users))
    jaccard_matrix = np.zeros((n_users, n_users))
    overlap_matrix = np.zeros((n_users, n_users))
    
    # 计算余弦相似度
    sequence_strings = [' '.join(map(str, sample_sequences[uid])) for uid in user_ids]
    vectorizer = TfidfVectorizer()
    tfidf_matrix = vectorizer.fit_transform(sequence_strings)
    cosine_matrix = cosine_similarity(tfidf_matrix)
    
    # 计算Jaccard和重叠相似度
    for i in range(n_users):
        for j in range(i, n_users):
            if i == j:
                jaccard_matrix[i][j] = 1.0
                overlap_matrix[i][j] = 1.0
            else:
                seq1 = sample_sequences[user_ids[i]]
                seq2 = sample_sequences[user_ids[j]]
                
                # Jaccard相似度
                set1, set2 = set(seq1), set(seq2)
                intersection = len(set1.intersection(set2))
                union = len(set1.union(set2))
                jaccard_sim = intersection / union if union > 0 else 0
                jaccard_matrix[i][j] = jaccard_matrix[j][i] = jaccard_sim
                
                # 重叠比例（最长公共子序列）
                def lcs_length(s1, s2):
                    m, n = len(s1), len(s2)
                    dp = [[0] * (n + 1) for _ in range(m + 1)]
                    for x in range(1, m + 1):
                        for y in range(1, n + 1):
                            if s1[x-1] == s2[y-1]:
                                dp[x][y] = dp[x-1][y-1] + 1
                            else:
                                dp[x][y] = max(dp[x-1][y], dp[x][y-1])
                    return dp[m][n]
                
                lcs_len = lcs_length(seq1, seq2)
                overlap_sim = lcs_len / max(len(seq1), len(seq2))
                overlap_matrix[i][j] = overlap_matrix[j][i] = overlap_sim
    
    return cosine_matrix, jaccard_matrix, overlap_matrix, user_ids

def cluster_sequences(similarity_matrix, n_clusters=5):
    """基于相似度矩阵进行聚类"""
    # 使用相似度矩阵进行聚类
    distance_matrix = 1 - similarity_matrix
    
    # 使用PCA降维
    pca = PCA(n_components=min(10, similarity_matrix.shape[0]-1))
    reduced_features = pca.fit_transform(similarity_matrix)
    
    # K-means聚类
    kmeans = KMeans(n_clusters=n_clusters, random_state=42)
    cluster_labels = kmeans.fit_predict(reduced_features)
    
    return cluster_labels, reduced_features, pca

def generate_comprehensive_report(sequences):
    """生成综合分析报告"""
    print("=" * 60)
    print("MOOC序列相似度综合分析报告")
    print("=" * 60)
    
    # 基本统计
    print(f"\n1. 数据集基本信息:")
    print(f"   - 用户总数: {len(sequences):,}")
    
    all_items = set()
    seq_lengths = []
    for seq in sequences.values():
        all_items.update(seq)
        seq_lengths.append(len(seq))
    
    print(f"   - 唯一项目数: {len(all_items):,}")
    print(f"   - 平均序列长度: {np.mean(seq_lengths):.2f}")
    print(f"   - 序列长度范围: {min(seq_lengths)} - {max(seq_lengths)}")
    
    # 项目频率分析
    item_counts = analyze_item_frequency(sequences)
    print(f"\n2. 项目频率分析:")
    print(f"   - 最热门项目: {item_counts.most_common(5)}")
    print(f"   - 出现次数≥10的项目数: {sum(1 for count in item_counts.values() if count >= 10)}")
    print(f"   - 只出现1次的项目数: {sum(1 for count in item_counts.values() if count == 1)}")
    
    # 序列模式分析
    patterns = analyze_sequence_patterns(sequences)
    print(f"\n3. 序列模式分析:")
    print(f"   - 包含重复项目的序列数: {patterns['repeated_items']:,}")
    print(f"   - 唯一序列数: {patterns['unique_sequences']:,}")
    print(f"   - 序列多样性: {patterns['sequence_diversity']:.4f}")
    print(f"   - 平均每序列唯一项目数: {patterns['avg_unique_items_per_seq']:.2f}")
    
    # 相似度分析
    print(f"\n4. 相似度分析 (基于前200个用户):")
    cosine_matrix, jaccard_matrix, overlap_matrix, user_ids = calculate_similarity_metrics(sequences, 200)
    
    # 提取上三角矩阵（排除对角线）
    def get_upper_triangle(matrix):
        return matrix[np.triu_indices_from(matrix, k=1)]
    
    cosine_vals = get_upper_triangle(cosine_matrix)
    jaccard_vals = get_upper_triangle(jaccard_matrix)
    overlap_vals = get_upper_triangle(overlap_matrix)
    
    print(f"   余弦相似度:")
    print(f"     - 平均值: {np.mean(cosine_vals):.4f}")
    print(f"     - 标准差: {np.std(cosine_vals):.4f}")
    print(f"     - 最大值: {np.max(cosine_vals):.4f}")
    print(f"     - 高相似度对数 (>0.3): {np.sum(cosine_vals > 0.3)}")
    
    print(f"   Jaccard相似度:")
    print(f"     - 平均值: {np.mean(jaccard_vals):.4f}")
    print(f"     - 标准差: {np.std(jaccard_vals):.4f}")
    print(f"     - 最大值: {np.max(jaccard_vals):.4f}")
    print(f"     - 高相似度对数 (>0.2): {np.sum(jaccard_vals > 0.2)}")
    
    print(f"   序列重叠比例:")
    print(f"     - 平均值: {np.mean(overlap_vals):.4f}")
    print(f"     - 标准差: {np.std(overlap_vals):.4f}")
    print(f"     - 最大值: {np.max(overlap_vals):.4f}")
    print(f"     - 高相似度对数 (>0.2): {np.sum(overlap_vals > 0.2)}")
    
    # 聚类分析
    print(f"\n5. 聚类分析:")
    cluster_labels, reduced_features, pca = cluster_sequences(cosine_matrix, n_clusters=5)
    cluster_counts = Counter(cluster_labels)
    print(f"   - 聚类数量: 5")
    print(f"   - 各聚类大小: {dict(cluster_counts)}")
    print(f"   - PCA解释方差比: {pca.explained_variance_ratio_[:3]}")
    
    # 生成可视化
    create_visualizations(sequences, cosine_matrix, jaccard_matrix, overlap_matrix, 
                         cluster_labels, reduced_features, item_counts)
    
    print(f"\n6. 文件输出:")
    print(f"   - 详细统计报告: detailed_analysis_report.txt")
    print(f"   - 可视化图表: comprehensive_analysis_plots.png")
    print(f"   - 聚类结果: sequence_clusters.csv")
    
    # 保存详细报告到文件
    save_detailed_report(sequences, cosine_matrix, jaccard_matrix, overlap_matrix, 
                        cluster_labels, user_ids, item_counts, patterns)

def create_visualizations(sequences, cosine_matrix, jaccard_matrix, overlap_matrix, 
                         cluster_labels, reduced_features, item_counts):
    """创建综合可视化图表"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 序列长度分布
    seq_lengths = [len(seq) for seq in sequences.values()]
    axes[0, 0].hist(seq_lengths, bins=50, alpha=0.7, edgecolor='black')
    axes[0, 0].set_title('序列长度分布')
    axes[0, 0].set_xlabel('序列长度')
    axes[0, 0].set_ylabel('频次')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 项目频率分布（Top 20）
    top_items = item_counts.most_common(20)
    items, counts = zip(*top_items)
    axes[0, 1].bar(range(len(items)), counts)
    axes[0, 1].set_title('Top 20 热门项目')
    axes[0, 1].set_xlabel('项目排名')
    axes[0, 1].set_ylabel('出现次数')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 相似度分布对比
    cosine_vals = cosine_matrix[np.triu_indices_from(cosine_matrix, k=1)]
    jaccard_vals = jaccard_matrix[np.triu_indices_from(jaccard_matrix, k=1)]
    overlap_vals = overlap_matrix[np.triu_indices_from(overlap_matrix, k=1)]
    
    axes[0, 2].hist(cosine_vals, bins=30, alpha=0.5, label='余弦相似度', density=True)
    axes[0, 2].hist(jaccard_vals, bins=30, alpha=0.5, label='Jaccard相似度', density=True)
    axes[0, 2].hist(overlap_vals, bins=30, alpha=0.5, label='重叠比例', density=True)
    axes[0, 2].set_title('相似度分布对比')
    axes[0, 2].set_xlabel('相似度值')
    axes[0, 2].set_ylabel('密度')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. 余弦相似度热力图
    sample_size = min(30, cosine_matrix.shape[0])
    sns.heatmap(cosine_matrix[:sample_size, :sample_size], 
                ax=axes[1, 0], cmap='viridis', square=True)
    axes[1, 0].set_title(f'余弦相似度热力图 (前{sample_size}用户)')
    
    # 5. 聚类结果可视化
    if reduced_features.shape[1] >= 2:
        scatter = axes[1, 1].scatter(reduced_features[:, 0], reduced_features[:, 1], 
                                   c=cluster_labels, cmap='tab10', alpha=0.7)
        axes[1, 1].set_title('序列聚类结果 (PCA降维)')
        axes[1, 1].set_xlabel('PC1')
        axes[1, 1].set_ylabel('PC2')
        plt.colorbar(scatter, ax=axes[1, 1])
    
    # 6. 相似度统计箱线图
    similarity_data = [cosine_vals, jaccard_vals, overlap_vals]
    axes[1, 2].boxplot(similarity_data, labels=['余弦', 'Jaccard', '重叠'])
    axes[1, 2].set_title('相似度统计分布')
    axes[1, 2].set_ylabel('相似度值')
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('comprehensive_analysis_plots.png', dpi=300, bbox_inches='tight')
    plt.close()

def save_detailed_report(sequences, cosine_matrix, jaccard_matrix, overlap_matrix, 
                        cluster_labels, user_ids, item_counts, patterns):
    """保存详细报告到文件"""
    with open('detailed_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write("MOOC序列相似度详细分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        # 基本统计
        f.write("1. 数据集基本统计\n")
        f.write(f"用户总数: {len(sequences)}\n")
        f.write(f"唯一项目数: {len(set().union(*sequences.values()))}\n")
        f.write(f"序列模式统计: {patterns}\n\n")
        
        # 项目频率
        f.write("2. 项目频率统计\n")
        f.write("Top 10 热门项目:\n")
        for i, (item, count) in enumerate(item_counts.most_common(10), 1):
            f.write(f"  {i}. 项目{item}: {count}次\n")
        f.write("\n")
        
        # 相似度统计
        cosine_vals = cosine_matrix[np.triu_indices_from(cosine_matrix, k=1)]
        jaccard_vals = jaccard_matrix[np.triu_indices_from(jaccard_matrix, k=1)]
        overlap_vals = overlap_matrix[np.triu_indices_from(overlap_matrix, k=1)]
        
        f.write("3. 相似度详细统计\n")
        f.write(f"余弦相似度: 均值={np.mean(cosine_vals):.4f}, 标准差={np.std(cosine_vals):.4f}\n")
        f.write(f"Jaccard相似度: 均值={np.mean(jaccard_vals):.4f}, 标准差={np.std(jaccard_vals):.4f}\n")
        f.write(f"重叠比例: 均值={np.mean(overlap_vals):.4f}, 标准差={np.std(overlap_vals):.4f}\n\n")
        
        # 聚类结果
        f.write("4. 聚类分析结果\n")
        cluster_counts = Counter(cluster_labels)
        for cluster_id, count in sorted(cluster_counts.items()):
            f.write(f"聚类{cluster_id}: {count}个用户\n")
    
    # 保存聚类结果
    cluster_df = pd.DataFrame({
        'User_ID': user_ids,
        'Cluster': cluster_labels
    })
    cluster_df.to_csv('sequence_clusters.csv', index=False)

def main():
    """主函数"""
    sequences = load_sequences('data/mooc.txt')
    generate_comprehensive_report(sequences)
    print("\n分析完成！所有结果已保存。")

if __name__ == "__main__":
    main()
