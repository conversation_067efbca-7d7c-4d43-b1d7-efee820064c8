#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
序列相似度分析脚本
分析mooc.txt中各个用户序列之间的相似度
"""

import numpy as np
import pandas as pd
from collections import Counter
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
import warnings
warnings.filterwarnings('ignore')

def load_sequences(file_path):
    """
    加载序列数据
    """
    sequences = {}
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split()
            if len(parts) >= 2:
                user_id = int(parts[0])
                sequence = [int(x) for x in parts[1:]]
                sequences[user_id] = sequence
    return sequences

def jaccard_similarity(seq1, seq2):
    """
    计算两个序列的Jaccard相似度
    """
    set1 = set(seq1)
    set2 = set(seq2)
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    return intersection / union if union > 0 else 0

def sequence_overlap_ratio(seq1, seq2):
    """
    计算序列重叠比例（考虑顺序）
    """
    if not seq1 or not seq2:
        return 0
    
    # 找到最长公共子序列
    def lcs_length(s1, s2):
        m, n = len(s1), len(s2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i-1] == s2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])
        
        return dp[m][n]
    
    lcs_len = lcs_length(seq1, seq2)
    return lcs_len / max(len(seq1), len(seq2))

def cosine_similarity_sequences(sequences):
    """
    使用TF-IDF向量化计算序列的余弦相似度
    """
    # 将序列转换为字符串格式
    sequence_strings = []
    user_ids = []
    
    for user_id, sequence in sequences.items():
        sequence_str = ' '.join(map(str, sequence))
        sequence_strings.append(sequence_str)
        user_ids.append(user_id)
    
    # 使用TF-IDF向量化
    vectorizer = TfidfVectorizer()
    tfidf_matrix = vectorizer.fit_transform(sequence_strings)
    
    # 计算余弦相似度矩阵
    cosine_sim_matrix = cosine_similarity(tfidf_matrix)
    
    return cosine_sim_matrix, user_ids

def analyze_sequence_statistics(sequences):
    """
    分析序列的基本统计信息
    """
    stats = {
        'total_users': len(sequences),
        'sequence_lengths': [len(seq) for seq in sequences.values()],
        'unique_items': set()
    }
    
    for seq in sequences.values():
        stats['unique_items'].update(seq)
    
    stats['total_unique_items'] = len(stats['unique_items'])
    stats['avg_sequence_length'] = np.mean(stats['sequence_lengths'])
    stats['median_sequence_length'] = np.median(stats['sequence_lengths'])
    stats['min_sequence_length'] = min(stats['sequence_lengths'])
    stats['max_sequence_length'] = max(stats['sequence_lengths'])
    
    return stats

def plot_similarity_distribution(similarity_matrix, title, save_path=None):
    """
    绘制相似度分布图
    """
    # 提取上三角矩阵（排除对角线）
    upper_triangle = np.triu(similarity_matrix, k=1)
    similarities = upper_triangle[upper_triangle > 0]
    
    plt.figure(figsize=(10, 6))
    plt.hist(similarities, bins=50, alpha=0.7, edgecolor='black')
    plt.title(f'{title} - 相似度分布')
    plt.xlabel('相似度')
    plt.ylabel('频次')
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    plt.axvline(np.mean(similarities), color='red', linestyle='--', 
                label=f'平均值: {np.mean(similarities):.3f}')
    plt.axvline(np.median(similarities), color='green', linestyle='--', 
                label=f'中位数: {np.median(similarities):.3f}')
    plt.legend()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存
    
    return similarities

def find_most_similar_pairs(similarity_matrix, user_ids, top_k=10):
    """
    找到最相似的序列对
    """
    n = len(user_ids)
    similar_pairs = []
    
    for i in range(n):
        for j in range(i+1, n):
            similarity = similarity_matrix[i][j]
            similar_pairs.append((user_ids[i], user_ids[j], similarity))
    
    # 按相似度排序
    similar_pairs.sort(key=lambda x: x[2], reverse=True)
    
    return similar_pairs[:top_k]

def main():
    """
    主函数
    """
    print("开始分析MOOC序列相似度...")
    
    # 加载数据
    sequences = load_sequences('data/mooc.txt')
    print(f"成功加载 {len(sequences)} 个用户序列")
    
    # 基本统计分析
    stats = analyze_sequence_statistics(sequences)
    print("\n=== 序列基本统计信息 ===")
    print(f"用户总数: {stats['total_users']}")
    print(f"唯一项目总数: {stats['total_unique_items']}")
    print(f"平均序列长度: {stats['avg_sequence_length']:.2f}")
    print(f"中位数序列长度: {stats['median_sequence_length']:.2f}")
    print(f"最短序列长度: {stats['min_sequence_length']}")
    print(f"最长序列长度: {stats['max_sequence_length']}")
    
    # 序列长度分布
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.hist(stats['sequence_lengths'], bins=50, alpha=0.7, edgecolor='black')
    plt.title('序列长度分布')
    plt.xlabel('序列长度')
    plt.ylabel('用户数量')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.boxplot(stats['sequence_lengths'])
    plt.title('序列长度箱线图')
    plt.ylabel('序列长度')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('sequence_length_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 为了计算效率，我们选择前100个用户进行相似度分析
    sample_sequences = dict(list(sequences.items())[:100])
    print(f"\n选择前 {len(sample_sequences)} 个用户进行相似度分析...")
    
    # 1. 余弦相似度分析
    print("\n=== 余弦相似度分析 ===")
    cosine_sim_matrix, user_ids = cosine_similarity_sequences(sample_sequences)
    cosine_similarities = plot_similarity_distribution(cosine_sim_matrix, "余弦相似度", 
                                                     "cosine_similarity_distribution.png")
    
    print(f"余弦相似度统计:")
    print(f"  平均值: {np.mean(cosine_similarities):.4f}")
    print(f"  标准差: {np.std(cosine_similarities):.4f}")
    print(f"  最大值: {np.max(cosine_similarities):.4f}")
    print(f"  最小值: {np.min(cosine_similarities):.4f}")
    
    # 找到最相似的序列对
    top_cosine_pairs = find_most_similar_pairs(cosine_sim_matrix, user_ids, top_k=10)
    print(f"\n余弦相似度最高的10对用户:")
    for i, (user1, user2, sim) in enumerate(top_cosine_pairs, 1):
        print(f"  {i}. 用户{user1} vs 用户{user2}: {sim:.4f}")
    
    # 2. Jaccard相似度分析
    print("\n=== Jaccard相似度分析 ===")
    n_users = len(sample_sequences)
    jaccard_matrix = np.zeros((n_users, n_users))
    
    user_list = list(sample_sequences.keys())
    for i in range(n_users):
        for j in range(i, n_users):
            if i == j:
                jaccard_matrix[i][j] = 1.0
            else:
                sim = jaccard_similarity(sample_sequences[user_list[i]], 
                                       sample_sequences[user_list[j]])
                jaccard_matrix[i][j] = sim
                jaccard_matrix[j][i] = sim
    
    jaccard_similarities = plot_similarity_distribution(jaccard_matrix, "Jaccard相似度",
                                                       "jaccard_similarity_distribution.png")
    
    print(f"Jaccard相似度统计:")
    print(f"  平均值: {np.mean(jaccard_similarities):.4f}")
    print(f"  标准差: {np.std(jaccard_similarities):.4f}")
    print(f"  最大值: {np.max(jaccard_similarities):.4f}")
    print(f"  最小值: {np.min(jaccard_similarities):.4f}")
    
    # 找到最相似的序列对
    top_jaccard_pairs = find_most_similar_pairs(jaccard_matrix, user_list, top_k=10)
    print(f"\nJaccard相似度最高的10对用户:")
    for i, (user1, user2, sim) in enumerate(top_jaccard_pairs, 1):
        print(f"  {i}. 用户{user1} vs 用户{user2}: {sim:.4f}")
    
    # 3. 序列重叠比例分析
    print("\n=== 序列重叠比例分析 ===")
    overlap_matrix = np.zeros((n_users, n_users))
    
    for i in range(n_users):
        for j in range(i, n_users):
            if i == j:
                overlap_matrix[i][j] = 1.0
            else:
                sim = sequence_overlap_ratio(sample_sequences[user_list[i]], 
                                           sample_sequences[user_list[j]])
                overlap_matrix[i][j] = sim
                overlap_matrix[j][i] = sim
    
    overlap_similarities = plot_similarity_distribution(overlap_matrix, "序列重叠比例",
                                                       "overlap_similarity_distribution.png")
    
    print(f"序列重叠比例统计:")
    print(f"  平均值: {np.mean(overlap_similarities):.4f}")
    print(f"  标准差: {np.std(overlap_similarities):.4f}")
    print(f"  最大值: {np.max(overlap_similarities):.4f}")
    print(f"  最小值: {np.min(overlap_similarities):.4f}")
    
    # 相似度矩阵热力图
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    sns.heatmap(cosine_sim_matrix[:20, :20], cmap='viridis', square=True)
    plt.title('余弦相似度矩阵 (前20用户)')
    
    plt.subplot(1, 3, 2)
    sns.heatmap(jaccard_matrix[:20, :20], cmap='viridis', square=True)
    plt.title('Jaccard相似度矩阵 (前20用户)')
    
    plt.subplot(1, 3, 3)
    sns.heatmap(overlap_matrix[:20, :20], cmap='viridis', square=True)
    plt.title('序列重叠比例矩阵 (前20用户)')
    
    plt.tight_layout()
    plt.savefig('similarity_heatmaps.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 保存结果到CSV
    results_df = pd.DataFrame({
        'User1': [pair[0] for pair in top_cosine_pairs],
        'User2': [pair[1] for pair in top_cosine_pairs],
        'Cosine_Similarity': [pair[2] for pair in top_cosine_pairs],
        'Jaccard_Similarity': [jaccard_matrix[user_list.index(pair[0])][user_list.index(pair[1])] 
                              for pair in top_cosine_pairs],
        'Overlap_Ratio': [overlap_matrix[user_list.index(pair[0])][user_list.index(pair[1])] 
                         for pair in top_cosine_pairs]
    })
    
    results_df.to_csv('top_similar_pairs.csv', index=False)
    print(f"\n结果已保存到 'top_similar_pairs.csv'")
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
